import Database from '@tauri-apps/plugin-sql';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  Package,
  Item,
  Batch,
  InventoryTransaction,
  PurchaseOrder,
  ShipmentOrder,
  StockInfo,
  CreatePackageRequest,
  CreateItemRequest,
  CreateBatchRequest,
  CreateInventoryTransactionRequest,
  CreatePurchaseOrderRequest,
  CreateShipmentOrderRequest,
} from '../types';

// Database connection
let db: Database | null = null;

const getDb = async () => {
  if (!db) {
    db = await Database.load('sqlite:fishtouch.db');
  }
  return db;
};

// Package hooks
export const usePackages = () => {
  return useQuery({
    queryKey: ['packages'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<Package[]>('SELECT id, name, description FROM packages ORDER BY name');
      return result;
    },
  });
};

export const useCreatePackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreatePackageRequest) => {
      const database = await getDb();
      const result = await database.execute(
        'INSERT INTO packages (name, description) VALUES (?, ?)',
        [data.name, data.description || '']
      );
      return {
        id: result.lastInsertId as number,
        name: data.name,
        description: data.description,
      } as Package;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages'] });
    },
  });
};

// Item hooks
export const useItems = () => {
  return useQuery({
    queryKey: ['items'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<Item[]>('SELECT id, name, sku FROM items ORDER BY name');
      return result;
    },
  });
};

export const useCreateItem = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreateItemRequest) => {
      const database = await getDb();
      const result = await database.execute(
        'INSERT INTO items (name, sku) VALUES (?, ?)',
        [data.name, data.sku]
      );
      return {
        id: result.lastInsertId as number,
        name: data.name,
        sku: data.sku,
      } as Item;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['items'] });
    },
  });
};

// Batch hooks
export const useBatches = () => {
  return useQuery({
    queryKey: ['batches'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<Batch[]>(
        'SELECT id, item_id, batch_number, in_date, expiry_date FROM batches ORDER BY in_date DESC'
      );
      return result;
    },
  });
};

export const useCreateBatch = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreateBatchRequest) => {
      const database = await getDb();
      const result = await database.execute(
        'INSERT INTO batches (item_id, batch_number, in_date, expiry_date) VALUES (?, ?, ?, ?)',
        [data.item_id, data.batch_number, data.in_date, data.expiry_date || '']
      );
      return {
        id: result.lastInsertId as number,
        item_id: data.item_id,
        batch_number: data.batch_number,
        in_date: data.in_date,
        expiry_date: data.expiry_date,
      } as Batch;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['batches'] });
      queryClient.invalidateQueries({ queryKey: ['stock'] });
    },
  });
};

// Inventory Transaction hooks
export const useInventoryTransactions = () => {
  return useQuery({
    queryKey: ['transactions'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<InventoryTransaction[]>(
        'SELECT id, batch_id, change_quantity, change_time, order_number, order_type FROM inventory_transactions ORDER BY change_time DESC'
      );
      return result;
    },
  });
};

export const useCreateInventoryTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreateInventoryTransactionRequest) => {
      const database = await getDb();
      const result = await database.execute(
        'INSERT INTO inventory_transactions (batch_id, change_quantity, order_number, order_type) VALUES (?, ?, ?, ?)',
        [data.batch_id, data.change_quantity, data.order_number || '', data.order_type || '']
      );
      return {
        id: result.lastInsertId as number,
        batch_id: data.batch_id,
        change_quantity: data.change_quantity,
        change_time: new Date().toISOString(),
        order_number: data.order_number,
        order_type: data.order_type,
      } as InventoryTransaction;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.invalidateQueries({ queryKey: ['stock'] });
    },
  });
};

// Stock hooks
export const useCurrentStock = () => {
  return useQuery({
    queryKey: ['stock'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<StockInfo[]>(`
        SELECT
          i.id as item_id,
          i.name as item_name,
          i.sku,
          b.id as batch_id,
          b.batch_number,
          b.expiry_date,
          COALESCE(SUM(it.change_quantity), 0) as current_stock
        FROM items i
        LEFT JOIN batches b ON i.id = b.item_id
        LEFT JOIN inventory_transactions it ON b.id = it.batch_id
        GROUP BY i.id, b.id
        HAVING current_stock > 0
        ORDER BY i.name, b.expiry_date
      `);
      return result;
    },
  });
};

// Purchase Order hooks
export const usePurchaseOrders = () => {
  return useQuery({
    queryKey: ['purchase-orders'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<PurchaseOrder[]>(
        'SELECT order_number, order_date, supplier FROM purchase_orders ORDER BY order_date DESC'
      );
      return result;
    },
  });
};

export const useCreatePurchaseOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreatePurchaseOrderRequest) => {
      const database = await getDb();
      await database.execute(
        'INSERT INTO purchase_orders (order_number, order_date, supplier) VALUES (?, ?, ?)',
        [data.order_number, data.order_date, data.supplier]
      );
      return data as PurchaseOrder;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
    },
  });
};

// Shipment Order hooks
export const useShipmentOrders = () => {
  return useQuery({
    queryKey: ['shipment-orders'],
    queryFn: async () => {
      const database = await getDb();
      const result = await database.select<ShipmentOrder[]>(
        'SELECT order_number, order_date, customer FROM shipment_orders ORDER BY order_date DESC'
      );
      return result;
    },
  });
};

export const useCreateShipmentOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: CreateShipmentOrderRequest) => {
      const database = await getDb();
      await database.execute(
        'INSERT INTO shipment_orders (order_number, order_date, customer) VALUES (?, ?, ?)',
        [data.order_number, data.order_date, data.customer]
      );
      return data as ShipmentOrder;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipment-orders'] });
    },
  });
};

import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@chakra-ui/react';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import PackageManagement from './pages/PackageManagement';
import ItemManagement from './pages/ItemManagement';
import BatchManagement from './pages/BatchManagement';
import InventoryTransactions from './pages/InventoryTransactions';
import PurchaseOrders from './pages/PurchaseOrders';
import ShipmentOrders from './pages/ShipmentOrders';

function App() {
  return (
    <Box minH="100vh" bg="gray.50">
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/packages" element={<PackageManagement />} />
          <Route path="/items" element={<ItemManagement />} />
          <Route path="/batches" element={<BatchManagement />} />
          <Route path="/transactions" element={<InventoryTransactions />} />
          <Route path="/purchase-orders" element={<PurchaseOrders />} />
          <Route path="/shipment-orders" element={<ShipmentOrders />} />
        </Routes>
      </Layout>
    </Box>
  );
}

export default App;

import { Box, Text, Heading, Button, VStack } from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import Database from '@tauri-apps/plugin-sql';

function App() {
  const [dbStatus, setDbStatus] = useState<string>('未连接');
  const [packages, setPackages] = useState<any[]>([]);

  const testDatabase = async () => {
    try {
      setDbStatus('正在连接...');
      const db = await Database.load('sqlite:fishtouch.db');
      setDbStatus('连接成功');

      // 测试查询
      const result = await db.select('SELECT * FROM packages LIMIT 5') as any[];
      setPackages(result);
      setDbStatus(`连接成功 - 找到 ${result.length} 个套餐`);
    } catch (error) {
      console.error('数据库连接错误:', error);
      setDbStatus(`连接失败: ${error}`);
    }
  };

  useEffect(() => {
    testDatabase();
  }, []);

  return (
    <Box minH="100vh" bg="gray.50" p={8}>
      <Box bg="white" p={8} borderRadius="lg" shadow="md" maxW="800px" mx="auto">
        <Heading size="lg" mb={4} color="blue.600">
          🐟 FishTouchStore 库存管理系统
        </Heading>

        <VStack align="start" gap={3}>
          <Text fontSize="lg" color="gray.600">
            系统状态检查:
          </Text>
          <Text color="green.500">
            ✅ 前端界面已加载
          </Text>
          <Text color="green.500">
            ✅ Chakra UI 组件正常工作
          </Text>
          <Text color={dbStatus.includes('成功') ? 'green.500' : dbStatus.includes('失败') ? 'red.500' : 'orange.500'}>
            🔄 数据库状态: {dbStatus}
          </Text>

          {packages.length > 0 && (
            <Box mt={4}>
              <Text fontWeight="bold">数据库中的套餐:</Text>
              {packages.map((pkg, index) => (
                <Text key={index} fontSize="sm" color="gray.600">
                  - {pkg.name || '未命名套餐'}
                </Text>
              ))}
            </Box>
          )}

          <Button onClick={testDatabase} colorScheme="blue" mt={4}>
            重新测试数据库连接
          </Button>
        </VStack>
      </Box>
    </Box>
  );
}

export default App;

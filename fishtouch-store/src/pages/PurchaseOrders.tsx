import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';
import { Plus, FileText } from 'lucide-react';
import { usePurchaseOrders, useCreatePurchaseOrder } from '../hooks/useApi';
import type { CreatePurchaseOrderRequest } from '../types';
import { format } from 'date-fns';

const PurchaseOrders: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreatePurchaseOrderRequest>({
    order_number: '',
    order_date: '',
    supplier: '',
  });

  const { data: orders, isLoading, error } = usePurchaseOrders();
  const createOrderMutation = useCreatePurchaseOrder();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.order_number.trim() || !formData.order_date || !formData.supplier.trim()) {
      toast({
        title: '错误',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      // 将日期转换为datetime格式
      const orderDateTime = new Date(formData.order_date).toISOString();
      await createOrderMutation.mutateAsync({
        ...formData,
        order_date: orderDateTime,
      });
      toast({
        title: '成功',
        description: '采购单创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({
        order_number: '',
        order_date: '',
        supplier: '',
      });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建采购单失败，单号可能已存在',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreatePurchaseOrderRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载采购单数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">采购单管理</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增采购单
        </Button>
      </HStack>

      {/* 采购单列表 */}
      <Card.Root>
        <Card.Header>
          <HStack>
            <FileText size={20} />
            <Text fontSize="lg" fontWeight="semibold">采购单列表</Text>
          </HStack>
        </Card.Header>
        <Card.Body>
          {orders && orders.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>采购单号</Th>
                  <Th>采购日期</Th>
                  <Th>供应商</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {orders.map((order) => (
                  <Tr key={order.order_number}>
                    <Td>
                      <Badge variant="outline" colorScheme="blue">
                        {order.order_number}
                      </Badge>
                    </Td>
                    <Td>
                      {format(new Date(order.order_date), 'yyyy-MM-dd HH:mm')}
                    </Td>
                    <Td fontWeight="medium">{order.supplier}</Td>
                    <Td>
                      <Badge colorScheme="green">已完成</Badge>
                    </Td>
                    <Td>
                      <HStack gap={2}>
                        <Button size="sm" variant="outline">
                          查看详情
                        </Button>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                        <Button size="sm" variant="outline" colorScheme="red">
                          删除
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <FileText size={48} color="#CBD5E0" style={{ margin: '0 auto 16px' }} />
              <Text color="gray.500">暂无采购单数据</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一个采购单
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增采购单模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增采购单</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>采购单号</FormLabel>
                  <Input
                    value={formData.order_number}
                    onChange={(e) => handleInputChange('order_number', e.target.value)}
                    placeholder="请输入采购单号"
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>采购日期</FormLabel>
                  <Input
                    type="datetime-local"
                    value={formData.order_date}
                    onChange={(e) => handleInputChange('order_date', e.target.value)}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>供应商</FormLabel>
                  <Input
                    value={formData.supplier}
                    onChange={(e) => handleInputChange('supplier', e.target.value)}
                    placeholder="请输入供应商名称"
                  />
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createOrderMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default PurchaseOrders;

import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';
import { Plus } from 'lucide-react';
import { useItems, useCreateItem } from '../hooks/useApi';
import type { CreateItemRequest } from '../types';

const ItemManagement: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreateItemRequest>({
    name: '',
    sku: '',
  });

  const { data: items, isLoading, error } = useItems();
  const createItemMutation = useCreateItem();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.sku.trim()) {
      toast({
        title: '错误',
        description: '单品名称和SKU都不能为空',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      await createItemMutation.mutateAsync(formData);
      toast({
        title: '成功',
        description: '单品创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({ name: '', sku: '' });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建单品失败，SKU可能已存在',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreateItemRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载单品数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">单品管理</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增单品
        </Button>
      </HStack>

      {/* 单品列表 */}
      <Card.Root>
        <Card.Header>
          <Text fontSize="lg" fontWeight="semibold">单品列表</Text>
        </Card.Header>
        <Card.Body>
          {items && items.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>单品名称</Th>
                  <Th>SKU</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {items.map((item) => (
                  <Tr key={item.id}>
                    <Td>{item.id}</Td>
                    <Td fontWeight="medium">{item.name}</Td>
                    <Td>
                      <Badge variant="outline" colorScheme="blue">
                        {item.sku}
                      </Badge>
                    </Td>
                    <Td>
                      <Badge colorScheme="green">正常</Badge>
                    </Td>
                    <Td>
                      <HStack gap={2}>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                        <Button size="sm" variant="outline" colorScheme="red">
                          删除
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">暂无单品数据</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一个单品
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增单品模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增单品</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>单品名称</FormLabel>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="请输入单品名称"
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>SKU</FormLabel>
                  <Input
                    value={formData.sku}
                    onChange={(e) => handleInputChange('sku', e.target.value)}
                    placeholder="请输入SKU编码"
                  />
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createItemMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default ItemManagement;

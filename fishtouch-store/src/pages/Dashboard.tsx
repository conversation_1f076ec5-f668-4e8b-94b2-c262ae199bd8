import React from 'react';
import {
  Box,
  Grid,
  Card,
  Text,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { Package, ShoppingCart, Archive, TrendingUp } from 'lucide-react';
import { useCurrentStock, usePackages, useItems, useBatches } from '../hooks/useApi';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const Dashboard: React.FC = () => {
  const { data: stockData, isLoading: stockLoading, error: stockError } = useCurrentStock();
  const { data: packages, isLoading: packagesLoading } = usePackages();
  const { data: items, isLoading: itemsLoading } = useItems();
  const { data: batches, isLoading: batchesLoading } = useBatches();

  const totalStock = stockData?.reduce((sum, item) => sum + item.current_stock, 0) || 0;
  const lowStockItems = stockData?.filter(item => item.current_stock < 50) || [];
  const expiringBatches = stockData?.filter(item => {
    if (!item.expiry_date) return false;
    const expiryDate = new Date(item.expiry_date);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7 && daysUntilExpiry >= 0;
  }) || [];

  if (stockLoading || packagesLoading || itemsLoading || batchesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (stockError) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 统计卡片 */}
      <Grid templateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={4}>
        <Card.Root>
          <Card.Body>
            <HStack>
              <Box p={3} bg="blue.100" borderRadius="lg">
                <Package size={24} color="#3182CE" />
              </Box>
              <Stat>
                <StatLabel>套餐总数</StatLabel>
                <StatNumber>{packages?.length || 0}</StatNumber>
                <StatHelpText>已配置套餐</StatHelpText>
              </Stat>
            </HStack>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <HStack>
              <Box p={3} bg="green.100" borderRadius="lg">
                <ShoppingCart size={24} color="#38A169" />
              </Box>
              <Stat>
                <StatLabel>单品种类</StatLabel>
                <StatNumber>{items?.length || 0}</StatNumber>
                <StatHelpText>管理中的单品</StatHelpText>
              </Stat>
            </HStack>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <HStack>
              <Box p={3} bg="purple.100" borderRadius="lg">
                <Archive size={24} color="#805AD5" />
              </Box>
              <Stat>
                <StatLabel>批次总数</StatLabel>
                <StatNumber>{batches?.length || 0}</StatNumber>
                <StatHelpText>活跃批次</StatHelpText>
              </Stat>
            </HStack>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <HStack>
              <Box p={3} bg="orange.100" borderRadius="lg">
                <TrendingUp size={24} color="#DD6B20" />
              </Box>
              <Stat>
                <StatLabel>总库存</StatLabel>
                <StatNumber>{totalStock}</StatNumber>
                <StatHelpText>当前库存总量</StatHelpText>
              </Stat>
            </HStack>
          </Card.Body>
        </Card.Root>
      </Grid>

      {/* 预警信息 */}
      <Grid templateColumns="repeat(auto-fit, minmax(400px, 1fr))" gap={6}>
        {/* 低库存预警 */}
        <Card.Root>
          <Card.Header>
            <Text fontSize="lg" fontWeight="semibold">低库存预警</Text>
          </Card.Header>
          <Card.Body>
            {lowStockItems.length === 0 ? (
              <Text color="gray.500">暂无低库存商品</Text>
            ) : (
              <VStack align="stretch" gap={2}>
                {lowStockItems.slice(0, 5).map((item) => (
                  <HStack key={`${item.item_id}-${item.batch_id}`} justify="space-between">
                    <VStack align="start" gap={0}>
                      <Text fontWeight="medium">{item.item_name}</Text>
                      <Text fontSize="sm" color="gray.500">{item.sku}</Text>
                    </VStack>
                    <Badge colorScheme="red">{item.current_stock}</Badge>
                  </HStack>
                ))}
              </VStack>
            )}
          </Card.Body>
        </Card.Root>

        {/* 即将过期批次 */}
        <Card.Root>
          <Card.Header>
            <Text fontSize="lg" fontWeight="semibold">即将过期</Text>
          </Card.Header>
          <Card.Body>
            {expiringBatches.length === 0 ? (
              <Text color="gray.500">暂无即将过期的批次</Text>
            ) : (
              <VStack align="stretch" gap={2}>
                {expiringBatches.slice(0, 5).map((item) => (
                  <HStack key={`${item.item_id}-${item.batch_id}`} justify="space-between">
                    <VStack align="start" gap={0}>
                      <Text fontWeight="medium">{item.item_name}</Text>
                      <Text fontSize="sm" color="gray.500">{item.batch_number}</Text>
                    </VStack>
                    <Badge colorScheme="orange">
                      {item.expiry_date && format(new Date(item.expiry_date), 'MM/dd', { locale: zhCN })}
                    </Badge>
                  </HStack>
                ))}
              </VStack>
            )}
          </Card.Body>
        </Card.Root>
      </Grid>

      {/* 当前库存概览 */}
      <Card.Root>
        <Card.Header>
          <Text fontSize="lg" fontWeight="semibold">当前库存概览</Text>
        </Card.Header>
        <Card.Body>
          <Table.Root size="sm">
            <Thead>
              <Tr>
                <Th>单品名称</Th>
                <Th>SKU</Th>
                <Th>批次号</Th>
                <Th>当前库存</Th>
                <Th>过期日期</Th>
              </Tr>
            </Thead>
            <Tbody>
              {stockData?.slice(0, 10).map((item) => (
                <Tr key={`${item.item_id}-${item.batch_id}`}>
                  <Td>{item.item_name}</Td>
                  <Td>{item.sku}</Td>
                  <Td>{item.batch_number}</Td>
                  <Td>
                    <Badge colorScheme={item.current_stock < 50 ? 'red' : 'green'}>
                      {item.current_stock}
                    </Badge>
                  </Td>
                  <Td>
                    {item.expiry_date ? 
                      format(new Date(item.expiry_date), 'yyyy-MM-dd', { locale: zhCN }) : 
                      '无'
                    }
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table.Root>
        </Card.Body>
      </Card.Root>
    </VStack>
  );
};

export default Dashboard;

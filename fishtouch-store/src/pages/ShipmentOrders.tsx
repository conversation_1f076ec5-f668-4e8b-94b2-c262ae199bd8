import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';
import { Plus, Truck } from 'lucide-react';
import { useShipmentOrders, useCreateShipmentOrder } from '../hooks/useApi';
import type { CreateShipmentOrderRequest } from '../types';
import { format } from 'date-fns';

const ShipmentOrders: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreateShipmentOrderRequest>({
    order_number: '',
    order_date: '',
    customer: '',
  });

  const { data: orders, isLoading, error } = useShipmentOrders();
  const createOrderMutation = useCreateShipmentOrder();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.order_number.trim() || !formData.order_date || !formData.customer.trim()) {
      toast({
        title: '错误',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      // 将日期转换为datetime格式
      const orderDateTime = new Date(formData.order_date).toISOString();
      await createOrderMutation.mutateAsync({
        ...formData,
        order_date: orderDateTime,
      });
      toast({
        title: '成功',
        description: '发货单创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({
        order_number: '',
        order_date: '',
        customer: '',
      });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建发货单失败，单号可能已存在',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreateShipmentOrderRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载发货单数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">发货单管理</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增发货单
        </Button>
      </HStack>

      {/* 发货单列表 */}
      <Card.Root>
        <Card.Header>
          <HStack>
            <Truck size={20} />
            <Text fontSize="lg" fontWeight="semibold">发货单列表</Text>
          </HStack>
        </Card.Header>
        <Card.Body>
          {orders && orders.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>发货单号</Th>
                  <Th>发货日期</Th>
                  <Th>客户</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {orders.map((order) => (
                  <Tr key={order.order_number}>
                    <Td>
                      <Badge variant="outline" colorScheme="orange">
                        {order.order_number}
                      </Badge>
                    </Td>
                    <Td>
                      {format(new Date(order.order_date), 'yyyy-MM-dd HH:mm')}
                    </Td>
                    <Td fontWeight="medium">{order.customer}</Td>
                    <Td>
                      <Badge colorScheme="green">已发货</Badge>
                    </Td>
                    <Td>
                      <HStack gap={2}>
                        <Button size="sm" variant="outline">
                          查看详情
                        </Button>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                        <Button size="sm" variant="outline" colorScheme="red">
                          删除
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <Truck size={48} color="#CBD5E0" style={{ margin: '0 auto 16px' }} />
              <Text color="gray.500">暂无发货单数据</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一个发货单
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增发货单模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增发货单</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>发货单号</FormLabel>
                  <Input
                    value={formData.order_number}
                    onChange={(e) => handleInputChange('order_number', e.target.value)}
                    placeholder="请输入发货单号"
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>发货日期</FormLabel>
                  <Input
                    type="datetime-local"
                    value={formData.order_date}
                    onChange={(e) => handleInputChange('order_date', e.target.value)}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>客户</FormLabel>
                  <Input
                    value={formData.customer}
                    onChange={(e) => handleInputChange('customer', e.target.value)}
                    placeholder="请输入客户名称"
                  />
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createOrderMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default ShipmentOrders;

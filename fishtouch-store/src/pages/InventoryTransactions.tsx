import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  NumberInput,
  NumberInputField,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';
import { Plus, TrendingUp, TrendingDown } from 'lucide-react';
import { useInventoryTransactions, useCreateInventoryTransaction, useBatches } from '../hooks/useApi';
import type { CreateInventoryTransactionRequest } from '../types';
import { format } from 'date-fns';

const InventoryTransactions: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreateInventoryTransactionRequest>({
    batch_id: 0,
    change_quantity: 0,
    order_number: '',
    order_type: undefined,
  });

  const { data: transactions, isLoading: transactionsLoading, error: transactionsError } = useInventoryTransactions();
  const { data: batches, isLoading: batchesLoading } = useBatches();
  const createTransactionMutation = useCreateInventoryTransaction();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.batch_id || formData.change_quantity === 0) {
      toast({
        title: '错误',
        description: '请选择批次并输入变动数量',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      await createTransactionMutation.mutateAsync(formData);
      toast({
        title: '成功',
        description: '库存流水记录成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({
        batch_id: 0,
        change_quantity: 0,
        order_number: '',
        order_type: undefined,
      });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建库存流水失败',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreateInventoryTransactionRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const getBatchInfo = (batchId: number) => {
    return batches?.find(batch => batch.id === batchId);
  };

  if (transactionsLoading || batchesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (transactionsError) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载库存流水数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">库存流水</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增流水
        </Button>
      </HStack>

      {/* 流水列表 */}
      <Card.Root>
        <Card.Header>
          <Text fontSize="lg" fontWeight="semibold">流水记录</Text>
        </Card.Header>
        <Card.Body>
          {transactions && transactions.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>批次信息</Th>
                  <Th>变动数量</Th>
                  <Th>变动时间</Th>
                  <Th>关联单号</Th>
                  <Th>单号类型</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {transactions.map((transaction) => {
                  const batchInfo = getBatchInfo(transaction.batch_id);
                  const isInbound = transaction.change_quantity > 0;
                  
                  return (
                    <Tr key={transaction.id}>
                      <Td>{transaction.id}</Td>
                      <Td>
                        <VStack align="start" gap={0}>
                          <Text fontSize="sm" fontWeight="medium">
                            {batchInfo?.batch_number || '未知批次'}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            批次ID: {transaction.batch_id}
                          </Text>
                        </VStack>
                      </Td>
                      <Td>
                        <HStack>
                          {isInbound ? (
                            <TrendingUp size={16} color="#38A169" />
                          ) : (
                            <TrendingDown size={16} color="#E53E3E" />
                          )}
                          <Badge colorScheme={isInbound ? 'green' : 'red'}>
                            {isInbound ? '+' : ''}{transaction.change_quantity}
                          </Badge>
                        </HStack>
                      </Td>
                      <Td>
                        {transaction.change_time ? 
                          format(new Date(transaction.change_time), 'yyyy-MM-dd HH:mm:ss') : 
                          '未知'
                        }
                      </Td>
                      <Td>{transaction.order_number || '无'}</Td>
                      <Td>
                        {transaction.order_type ? (
                          <Badge 
                            colorScheme={transaction.order_type === 'PURCHASE' ? 'blue' : 'orange'}
                          >
                            {transaction.order_type === 'PURCHASE' ? '采购' : '发货'}
                          </Badge>
                        ) : (
                          '无'
                        )}
                      </Td>
                      <Td>
                        <Button size="sm" variant="outline">
                          查看详情
                        </Button>
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">暂无流水记录</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一条流水记录
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增流水模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增库存流水</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>选择批次</FormLabel>
                  <Select
                    value={formData.batch_id}
                    onChange={(e) => handleInputChange('batch_id', parseInt(e.target.value))}
                    placeholder="请选择批次"
                  >
                    {batches?.map((batch) => (
                      <option key={batch.id} value={batch.id}>
                        {batch.batch_number} (ID: {batch.id})
                      </option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>变动数量</FormLabel>
                  <NumberInput>
                    <NumberInputField
                      value={formData.change_quantity}
                      onChange={(e) => handleInputChange('change_quantity', parseInt(e.target.value) || 0)}
                      placeholder="正数为入库，负数为出库"
                    />
                  </NumberInput>
                </FormControl>
                <FormControl>
                  <FormLabel>关联单号</FormLabel>
                  <Input
                    value={formData.order_number || ''}
                    onChange={(e) => handleInputChange('order_number', e.target.value)}
                    placeholder="请输入关联单号（可选）"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>单号类型</FormLabel>
                  <Select
                    value={formData.order_type || ''}
                    onChange={(e) => handleInputChange('order_type', e.target.value as 'PURCHASE' | 'SHIPMENT' | undefined)}
                    placeholder="请选择单号类型（可选）"
                  >
                    <option value="PURCHASE">采购单</option>
                    <option value="SHIPMENT">发货单</option>
                  </Select>
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createTransactionMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default InventoryTransactions;

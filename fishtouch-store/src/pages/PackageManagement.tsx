import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { Plus } from 'lucide-react';
import { usePackages, useCreatePackage } from '../hooks/useApi';
import type { CreatePackageRequest } from '../types';

const PackageManagement: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreatePackageRequest>({
    name: '',
    description: '',
  });

  const { data: packages, isLoading, error } = usePackages();
  const createPackageMutation = useCreatePackage();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast({
        title: '错误',
        description: '套餐名称不能为空',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      await createPackageMutation.mutateAsync(formData);
      toast({
        title: '成功',
        description: '套餐创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({ name: '', description: '' });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建套餐失败',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreatePackageRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value || undefined,
    }));
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载套餐数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">套餐管理</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增套餐
        </Button>
      </HStack>

      {/* 套餐列表 */}
      <Card.Root>
        <Card.Header>
          <Text fontSize="lg" fontWeight="semibold">套餐列表</Text>
        </Card.Header>
        <Card.Body>
          {packages && packages.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>套餐名称</Th>
                  <Th>描述</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {packages.map((pkg) => (
                  <Tr key={pkg.id}>
                    <Td>{pkg.id}</Td>
                    <Td fontWeight="medium">{pkg.name}</Td>
                    <Td color="gray.600">{pkg.description || '无描述'}</Td>
                    <Td>
                      <HStack gap={2}>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                        <Button size="sm" variant="outline" colorScheme="red">
                          删除
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">暂无套餐数据</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一个套餐
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增套餐模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增套餐</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>套餐名称</FormLabel>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="请输入套餐名称"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>描述</FormLabel>
                  <Textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="请输入套餐描述（可选）"
                    rows={3}
                  />
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createPackageMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default PackageManagement;

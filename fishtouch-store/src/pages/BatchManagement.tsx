import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';
import { Plus } from 'lucide-react';
import { useBatches, useCreateBatch, useItems } from '../hooks/useApi';
import type { CreateBatchRequest } from '../types';
import { format } from 'date-fns';

const BatchManagement: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [formData, setFormData] = useState<CreateBatchRequest>({
    item_id: 0,
    batch_number: '',
    in_date: '',
    expiry_date: '',
  });

  const { data: batches, isLoading: batchesLoading, error: batchesError } = useBatches();
  const { data: items, isLoading: itemsLoading } = useItems();
  const createBatchMutation = useCreateBatch();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.item_id || !formData.batch_number.trim() || !formData.in_date) {
      toast({
        title: '错误',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      await createBatchMutation.mutateAsync(formData);
      toast({
        title: '成功',
        description: '批次创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      setFormData({
        item_id: 0,
        batch_number: '',
        in_date: '',
        expiry_date: '',
      });
      onClose();
    } catch (error) {
      toast({
        title: '错误',
        description: '创建批次失败',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleInputChange = (field: keyof CreateBatchRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value || (field === 'item_id' ? 0 : ''),
    }));
  };

  const getItemName = (itemId: number) => {
    return items?.find(item => item.id === itemId)?.name || '未知单品';
  };

  if (batchesLoading || itemsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" h="400px">
        <Spinner size="xl" />
      </Box>
    );
  }

  if (batchesError) {
    return (
      <Alert status="error">
        <AlertIcon />
        加载批次数据时出错，请稍后重试
      </Alert>
    );
  }

  return (
    <VStack gap={6} align="stretch">
      {/* 页面头部 */}
      <HStack justify="space-between">
        <Text fontSize="2xl" fontWeight="bold">批次管理</Text>
        <Button
          leftIcon={<Plus size={18} />}
          colorScheme="blue"
          onClick={onOpen}
        >
          新增批次
        </Button>
      </HStack>

      {/* 批次列表 */}
      <Card.Root>
        <Card.Header>
          <Text fontSize="lg" fontWeight="semibold">批次列表</Text>
        </Card.Header>
        <Card.Body>
          {batches && batches.length > 0 ? (
            <Table.Root>
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>单品</Th>
                  <Th>批次号</Th>
                  <Th>入库日期</Th>
                  <Th>过期日期</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {batches.map((batch) => {
                  const isExpired = batch.expiry_date && new Date(batch.expiry_date) < new Date();
                  const isExpiringSoon = batch.expiry_date && 
                    new Date(batch.expiry_date) > new Date() &&
                    new Date(batch.expiry_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
                  
                  return (
                    <Tr key={batch.id}>
                      <Td>{batch.id}</Td>
                      <Td fontWeight="medium">{getItemName(batch.item_id)}</Td>
                      <Td>
                        <Badge variant="outline" colorScheme="blue">
                          {batch.batch_number}
                        </Badge>
                      </Td>
                      <Td>{format(new Date(batch.in_date), 'yyyy-MM-dd')}</Td>
                      <Td>
                        {batch.expiry_date ? 
                          format(new Date(batch.expiry_date), 'yyyy-MM-dd') : 
                          '无'
                        }
                      </Td>
                      <Td>
                        <Badge 
                          colorScheme={
                            isExpired ? 'red' : 
                            isExpiringSoon ? 'orange' : 
                            'green'
                          }
                        >
                          {isExpired ? '已过期' : isExpiringSoon ? '即将过期' : '正常'}
                        </Badge>
                      </Td>
                      <Td>
                        <HStack gap={2}>
                          <Button size="sm" variant="outline">
                            编辑
                          </Button>
                          <Button size="sm" variant="outline" colorScheme="red">
                            删除
                          </Button>
                        </HStack>
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table.Root>
          ) : (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">暂无批次数据</Text>
              <Button mt={4} colorScheme="blue" onClick={onOpen}>
                创建第一个批次
              </Button>
            </Box>
          )}
        </Card.Body>
      </Card.Root>

      {/* 新增批次模态框 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>新增批次</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack gap={4}>
                <FormControl isRequired>
                  <FormLabel>选择单品</FormLabel>
                  <Select
                    value={formData.item_id}
                    onChange={(e) => handleInputChange('item_id', parseInt(e.target.value))}
                    placeholder="请选择单品"
                  >
                    {items?.map((item) => (
                      <option key={item.id} value={item.id}>
                        {item.name} ({item.sku})
                      </option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>批次号</FormLabel>
                  <Input
                    value={formData.batch_number}
                    onChange={(e) => handleInputChange('batch_number', e.target.value)}
                    placeholder="请输入批次号"
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>入库日期</FormLabel>
                  <Input
                    type="date"
                    value={formData.in_date}
                    onChange={(e) => handleInputChange('in_date', e.target.value)}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>过期日期</FormLabel>
                  <Input
                    type="date"
                    value={formData.expiry_date || ''}
                    onChange={(e) => handleInputChange('expiry_date', e.target.value)}
                  />
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                取消
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={createBatchMutation.isPending}
              >
                创建
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default BatchManagement;

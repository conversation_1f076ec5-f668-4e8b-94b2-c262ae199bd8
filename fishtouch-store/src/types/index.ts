export interface Package {
  id?: number;
  name: string;
  description?: string;
}

export interface Item {
  id?: number;
  name: string;
  sku: string;
}

export interface Recipe {
  id?: number;
  package_id: number;
  item_id: number;
  quantity: number;
  unit: string;
  valid_from: string;
  valid_to?: string;
}

export interface Batch {
  id?: number;
  item_id: number;
  batch_number: string;
  in_date: string;
  expiry_date?: string;
}

export interface InventoryTransaction {
  id?: number;
  batch_id: number;
  change_quantity: number;
  change_time?: string;
  order_number?: string;
  order_type?: 'PURCHASE' | 'SHIPMENT';
}

export interface PurchaseOrder {
  order_number: string;
  order_date: string;
  supplier: string;
}

export interface ShipmentOrder {
  order_number: string;
  order_date: string;
  customer: string;
}

export interface InventoryAlert {
  id?: number;
  item_id: number;
  threshold: number;
  is_active: boolean;
  triggered_at?: string;
}

export interface StockInfo {
  item_id: number;
  item_name: string;
  sku: string;
  batch_id: number;
  batch_number: string;
  current_stock: number;
  expiry_date?: string;
}

export interface PackageWithRecipes {
  id: number;
  name: string;
  description?: string;
  recipes: RecipeWithItem[];
}

export interface RecipeWithItem {
  id: number;
  item_id: number;
  item_name: string;
  sku: string;
  quantity: number;
  unit: string;
  valid_from: string;
  valid_to?: string;
}

// 创建请求类型
export interface CreatePackageRequest {
  name: string;
  description?: string;
}

export interface CreateItemRequest {
  name: string;
  sku: string;
}

export interface CreateBatchRequest {
  item_id: number;
  batch_number: string;
  in_date: string;
  expiry_date?: string;
}

export interface CreateInventoryTransactionRequest {
  batch_id: number;
  change_quantity: number;
  order_number?: string;
  order_type?: 'PURCHASE' | 'SHIPMENT';
}

export interface CreatePurchaseOrderRequest {
  order_number: string;
  order_date: string;
  supplier: string;
}

export interface CreateShipmentOrderRequest {
  order_number: string;
  order_date: string;
  customer: string;
}

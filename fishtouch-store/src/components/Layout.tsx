import React from 'react';
import {
  Box,
  Flex,
  VStack,
  HStack,
  Text,
  Button,
  Separator,
  Container,
} from '@chakra-ui/react';
import { Link, useLocation } from 'react-router-dom';
import {
  Package,
  ShoppingCart,
  Archive,
  TrendingUp,
  FileText,
  Truck,
  BarChart3,
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const menuItems = [
    { path: '/', label: '仪表板', icon: BarChart3 },
    { path: '/packages', label: '套餐管理', icon: Package },
    { path: '/items', label: '单品管理', icon: ShoppingCart },
    { path: '/batches', label: '批次管理', icon: Archive },
    { path: '/transactions', label: '库存流水', icon: TrendingUp },
    { path: '/purchase-orders', label: '采购单', icon: FileText },
    { path: '/shipment-orders', label: '发货单', icon: Truck },
  ];

  return (
    <Flex h="100vh">
      {/* 侧边栏 */}
      <Box w="250px" bg="white" borderRight="1px" borderColor="gray.200" p={4}>
        <VStack align="stretch" gap={2}>
          <Text fontSize="xl" fontWeight="bold" mb={4} color="blue.600">
            鱼触店库存系统
          </Text>
          <Separator />
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            return (
              <Button
                key={item.path}
                as={Link}
                to={item.path}
                variant={isActive ? 'solid' : 'ghost'}
                colorScheme={isActive ? 'blue' : 'gray'}
                justifyContent="flex-start"
                leftIcon={<Icon size={18} />}
                size="md"
                w="full"
              >
                {item.label}
              </Button>
            );
          })}
        </VStack>
      </Box>

      {/* 主内容区域 */}
      <Box flex={1} overflow="auto">
        <Box bg="white" borderBottom="1px" borderColor="gray.200" p={4}>
          <Container maxW="container.xl">
            <HStack justify="space-between">
              <Text fontSize="lg" fontWeight="semibold">
                {menuItems.find(item => item.path === location.pathname)?.label || '仪表板'}
              </Text>
              <Text fontSize="sm" color="gray.500">
                {new Date().toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </Text>
            </HStack>
          </Container>
        </Box>
        
        <Container maxW="container.xl" p={6}>
          {children}
        </Container>
      </Box>
    </Flex>
  );
};

export default Layout;

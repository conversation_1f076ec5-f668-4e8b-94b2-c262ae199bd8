-- 创建套餐表
CREATE TABLE IF NOT EXISTS packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT
);

-- 创建单品表
CREATE TABLE IF NOT EXISTS items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    sku TEXT UNIQUE NOT NULL
);

-- 创建配方表
CREATE TABLE IF NOT EXISTS recipes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    package_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    quantity REAL NOT NULL,
    unit TEXT NOT NULL,
    valid_from DATE NOT NULL,
    valid_to DATE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);

-- 创建批次表
CREATE TABLE IF NOT EXISTS batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    batch_number TEXT NOT NULL,
    in_date DATE NOT NULL,
    expiry_date DATE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
    UNIQUE(item_id, batch_number)
);

-- 创建库存流水表
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id INTEGER NOT NULL,
    change_quantity INTEGER NOT NULL,
    change_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    order_number TEXT,
    order_type TEXT CHECK (order_type IN ('PURCHASE', 'SHIPMENT')),
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE
);

-- 创建采购单表
CREATE TABLE IF NOT EXISTS purchase_orders (
    order_number TEXT PRIMARY KEY,
    order_date DATETIME NOT NULL,
    supplier TEXT NOT NULL
);

-- 创建发货单表
CREATE TABLE IF NOT EXISTS shipment_orders (
    order_number TEXT PRIMARY KEY,
    order_date DATETIME NOT NULL,
    customer TEXT NOT NULL
);

-- 创建库存预警表
CREATE TABLE IF NOT EXISTS inventory_alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    threshold INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    triggered_at DATETIME,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_recipes_package_id ON recipes(package_id);
CREATE INDEX IF NOT EXISTS idx_recipes_item_id ON recipes(item_id);
CREATE INDEX IF NOT EXISTS idx_batches_item_id ON batches(item_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_batch_id ON inventory_transactions(batch_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_order_number ON inventory_transactions(order_number);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_item_id ON inventory_alerts(item_id);

-- 插入一些示例数据
INSERT OR IGNORE INTO items (name, sku) VALUES 
    ('鱼肉', 'FISH001'),
    ('虾仁', 'SHRIMP001'),
    ('蔬菜', 'VEG001'),
    ('调料包', 'SAUCE001');

INSERT OR IGNORE INTO packages (name, description) VALUES 
    ('经典套餐', '包含鱼肉和蔬菜的经典搭配'),
    ('豪华套餐', '包含鱼肉、虾仁和蔬菜的豪华搭配');

-- 为经典套餐添加配方
INSERT OR IGNORE INTO recipes (package_id, item_id, quantity, unit, valid_from) VALUES 
    (1, 1, 200, '克', '2024-01-01'),
    (1, 3, 100, '克', '2024-01-01'),
    (1, 4, 1, '包', '2024-01-01');

-- 为豪华套餐添加配方
INSERT OR IGNORE INTO recipes (package_id, item_id, quantity, unit, valid_from) VALUES 
    (2, 1, 250, '克', '2024-01-01'),
    (2, 2, 150, '克', '2024-01-01'),
    (2, 3, 150, '克', '2024-01-01'),
    (2, 4, 1, '包', '2024-01-01');

-- 添加一些批次数据
INSERT OR IGNORE INTO batches (item_id, batch_number, in_date, expiry_date) VALUES 
    (1, 'FISH001-20240101', '2024-01-01', '2024-01-15'),
    (2, 'SHRIMP001-20240101', '2024-01-01', '2024-01-10'),
    (3, 'VEG001-20240101', '2024-01-01', '2024-01-07'),
    (4, 'SAUCE001-20240101', '2024-01-01', '2024-12-31');

-- 添加初始库存
INSERT OR IGNORE INTO inventory_transactions (batch_id, change_quantity, order_number, order_type) VALUES 
    (1, 1000, 'PO-20240101-001', 'PURCHASE'),
    (2, 500, 'PO-20240101-001', 'PURCHASE'),
    (3, 800, 'PO-20240101-001', 'PURCHASE'),
    (4, 200, 'PO-20240101-001', 'PURCHASE');

-- 添加采购单
INSERT OR IGNORE INTO purchase_orders (order_number, order_date, supplier) VALUES 
    ('PO-20240101-001', '2024-01-01 10:00:00', '海鲜供应商A');

-- 设置库存预警
INSERT OR IGNORE INTO inventory_alerts (item_id, threshold, is_active) VALUES 
    (1, 100, 1),
    (2, 50, 1),
    (3, 80, 1),
    (4, 20, 1);

use serde::{Deserialize, Serialize};
use chrono::{DateTime, NaiveDate, Utc};

#[derive(Debug, Serialize, Deserialize)]
pub struct Package {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Item {
    pub id: Option<i64>,
    pub name: String,
    pub sku: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Recipe {
    pub id: Option<i64>,
    pub package_id: i64,
    pub item_id: i64,
    pub quantity: f64,
    pub unit: String,
    pub valid_from: String, // 使用字符串格式的日期
    pub valid_to: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Batch {
    pub id: Option<i64>,
    pub item_id: i64,
    pub batch_number: String,
    pub in_date: String,
    pub expiry_date: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InventoryTransaction {
    pub id: Option<i64>,
    pub batch_id: i64,
    pub change_quantity: i64,
    pub change_time: Option<String>,
    pub order_number: Option<String>,
    pub order_type: Option<String>, // PURCHASE or SHIPMENT
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PurchaseOrder {
    pub order_number: String,
    pub order_date: String,
    pub supplier: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ShipmentOrder {
    pub order_number: String,
    pub order_date: String,
    pub customer: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InventoryAlert {
    pub id: Option<i64>,
    pub item_id: i64,
    pub threshold: i64,
    pub is_active: bool,
    pub triggered_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StockInfo {
    pub item_id: i64,
    pub item_name: String,
    pub sku: String,
    pub batch_id: i64,
    pub batch_number: String,
    pub current_stock: i64,
    pub expiry_date: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PackageWithRecipes {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub recipes: Vec<RecipeWithItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RecipeWithItem {
    pub id: i64,
    pub item_id: i64,
    pub item_name: String,
    pub sku: String,
    pub quantity: f64,
    pub unit: String,
    pub valid_from: String,
    pub valid_to: Option<String>,
}

// 创建请求结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePackageRequest {
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateItemRequest {
    pub name: String,
    pub sku: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateBatchRequest {
    pub item_id: i64,
    pub batch_number: String,
    pub in_date: String,
    pub expiry_date: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateInventoryTransactionRequest {
    pub batch_id: i64,
    pub change_quantity: i64,
    pub order_number: Option<String>,
    pub order_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePurchaseOrderRequest {
    pub order_number: String,
    pub order_date: String,
    pub supplier: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateShipmentOrderRequest {
    pub order_number: String,
    pub order_date: String,
    pub customer: String,
}

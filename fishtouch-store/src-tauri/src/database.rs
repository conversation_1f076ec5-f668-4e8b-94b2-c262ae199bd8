use crate::models::*;
use tauri::State;
use tauri_plugin_sql::{Migration, MigrationKind};

// 套餐相关操作
#[tauri::command]
pub async fn create_package(
    app: tauri::AppHandle,
    request: CreatePackageRequest,
) -> Result<Package, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .execute(
            "INSERT INTO packages (name, description) VALUES (?, ?)",
            &[&request.name, &request.description.unwrap_or_default()],
        )
        .await
        .map_err(|e| e.to_string())?;

    Ok(Package {
        id: Some(result.last_insert_id()),
        name: request.name,
        description: request.description,
    })
}

#[tauri::command]
pub async fn get_packages(app: tauri::AppHandle) -> Result<Vec<Package>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT id, name, description FROM packages ORDER BY name")
        .await
        .map_err(|e| e.to_string())?;

    let packages: Vec<Package> = result
        .into_iter()
        .map(|row| Package {
            id: row.get::<i64, _>("id").ok(),
            name: row.get::<String, _>("name").unwrap_or_default(),
            description: row.get::<String, _>("description").ok(),
        })
        .collect();

    Ok(packages)
}

// 单品相关操作
#[tauri::command]
pub async fn create_item(
    app: tauri::AppHandle,
    request: CreateItemRequest,
) -> Result<Item, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .execute(
            "INSERT INTO items (name, sku) VALUES (?, ?)",
            &[&request.name, &request.sku],
        )
        .await
        .map_err(|e| e.to_string())?;

    Ok(Item {
        id: Some(result.last_insert_id()),
        name: request.name,
        sku: request.sku,
    })
}

#[tauri::command]
pub async fn get_items(app: tauri::AppHandle) -> Result<Vec<Item>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT id, name, sku FROM items ORDER BY name")
        .await
        .map_err(|e| e.to_string())?;

    let items: Vec<Item> = result
        .into_iter()
        .map(|row| Item {
            id: row.get::<i64, _>("id").ok(),
            name: row.get::<String, _>("name").unwrap_or_default(),
            sku: row.get::<String, _>("sku").unwrap_or_default(),
        })
        .collect();

    Ok(items)
}

// 批次相关操作
#[tauri::command]
pub async fn create_batch(
    app: tauri::AppHandle,
    request: CreateBatchRequest,
) -> Result<Batch, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .execute(
            "INSERT INTO batches (item_id, batch_number, in_date, expiry_date) VALUES (?, ?, ?, ?)",
            &[
                &request.item_id.to_string(),
                &request.batch_number,
                &request.in_date,
                &request.expiry_date.unwrap_or_default(),
            ],
        )
        .await
        .map_err(|e| e.to_string())?;

    Ok(Batch {
        id: Some(result.last_insert_id()),
        item_id: request.item_id,
        batch_number: request.batch_number,
        in_date: request.in_date,
        expiry_date: request.expiry_date,
    })
}

#[tauri::command]
pub async fn get_batches(app: tauri::AppHandle) -> Result<Vec<Batch>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT id, item_id, batch_number, in_date, expiry_date FROM batches ORDER BY in_date DESC")
        .await
        .map_err(|e| e.to_string())?;

    let batches: Vec<Batch> = result
        .into_iter()
        .map(|row| Batch {
            id: row.get::<i64, _>("id").ok(),
            item_id: row.get::<i64, _>("item_id").unwrap_or_default(),
            batch_number: row.get::<String, _>("batch_number").unwrap_or_default(),
            in_date: row.get::<String, _>("in_date").unwrap_or_default(),
            expiry_date: row.get::<String, _>("expiry_date").ok(),
        })
        .collect();

    Ok(batches)
}

// 库存流水相关操作
#[tauri::command]
pub async fn create_inventory_transaction(
    app: tauri::AppHandle,
    request: CreateInventoryTransactionRequest,
) -> Result<InventoryTransaction, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .execute(
            "INSERT INTO inventory_transactions (batch_id, change_quantity, order_number, order_type) VALUES (?, ?, ?, ?)",
            &[
                &request.batch_id.to_string(),
                &request.change_quantity.to_string(),
                &request.order_number.unwrap_or_default(),
                &request.order_type.unwrap_or_default(),
            ],
        )
        .await
        .map_err(|e| e.to_string())?;

    Ok(InventoryTransaction {
        id: Some(result.last_insert_id()),
        batch_id: request.batch_id,
        change_quantity: request.change_quantity,
        change_time: None, // 数据库会自动设置
        order_number: request.order_number,
        order_type: request.order_type,
    })
}

#[tauri::command]
pub async fn get_inventory_transactions(app: tauri::AppHandle) -> Result<Vec<InventoryTransaction>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT id, batch_id, change_quantity, change_time, order_number, order_type FROM inventory_transactions ORDER BY change_time DESC")
        .await
        .map_err(|e| e.to_string())?;

    let transactions: Vec<InventoryTransaction> = result
        .into_iter()
        .map(|row| InventoryTransaction {
            id: row.get::<i64, _>("id").ok(),
            batch_id: row.get::<i64, _>("batch_id").unwrap_or_default(),
            change_quantity: row.get::<i64, _>("change_quantity").unwrap_or_default(),
            change_time: row.get::<String, _>("change_time").ok(),
            order_number: row.get::<String, _>("order_number").ok(),
            order_type: row.get::<String, _>("order_type").ok(),
        })
        .collect();

    Ok(transactions)
}

// 获取当前库存
#[tauri::command]
pub async fn get_current_stock(app: tauri::AppHandle) -> Result<Vec<StockInfo>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select(
            "SELECT
                i.id as item_id,
                i.name as item_name,
                i.sku,
                b.id as batch_id,
                b.batch_number,
                b.expiry_date,
                COALESCE(SUM(it.change_quantity), 0) as current_stock
            FROM items i
            LEFT JOIN batches b ON i.id = b.item_id
            LEFT JOIN inventory_transactions it ON b.id = it.batch_id
            GROUP BY i.id, b.id
            HAVING current_stock > 0
            ORDER BY i.name, b.expiry_date"
        )
        .await
        .map_err(|e| e.to_string())?;

    let stock_info: Vec<StockInfo> = result
        .into_iter()
        .map(|row| StockInfo {
            item_id: row.get::<i64, _>("item_id").unwrap_or_default(),
            item_name: row.get::<String, _>("item_name").unwrap_or_default(),
            sku: row.get::<String, _>("sku").unwrap_or_default(),
            batch_id: row.get::<i64, _>("batch_id").unwrap_or_default(),
            batch_number: row.get::<String, _>("batch_number").unwrap_or_default(),
            current_stock: row.get::<i64, _>("current_stock").unwrap_or_default(),
            expiry_date: row.get::<String, _>("expiry_date").ok(),
        })
        .collect();

    Ok(stock_info)
}

// 采购单相关操作
#[tauri::command]
pub async fn create_purchase_order(
    app: tauri::AppHandle,
    request: CreatePurchaseOrderRequest,
) -> Result<PurchaseOrder, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    db.execute(
        "INSERT INTO purchase_orders (order_number, order_date, supplier) VALUES (?, ?, ?)",
        &[&request.order_number, &request.order_date, &request.supplier],
    )
    .await
    .map_err(|e| e.to_string())?;

    Ok(PurchaseOrder {
        order_number: request.order_number,
        order_date: request.order_date,
        supplier: request.supplier,
    })
}

#[tauri::command]
pub async fn get_purchase_orders(app: tauri::AppHandle) -> Result<Vec<PurchaseOrder>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT order_number, order_date, supplier FROM purchase_orders ORDER BY order_date DESC")
        .await
        .map_err(|e| e.to_string())?;

    let orders: Vec<PurchaseOrder> = result
        .into_iter()
        .map(|row| PurchaseOrder {
            order_number: row.get::<String, _>("order_number").unwrap_or_default(),
            order_date: row.get::<String, _>("order_date").unwrap_or_default(),
            supplier: row.get::<String, _>("supplier").unwrap_or_default(),
        })
        .collect();

    Ok(orders)
}

// 发货单相关操作
#[tauri::command]
pub async fn create_shipment_order(
    app: tauri::AppHandle,
    request: CreateShipmentOrderRequest,
) -> Result<ShipmentOrder, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    db.execute(
        "INSERT INTO shipment_orders (order_number, order_date, customer) VALUES (?, ?, ?)",
        &[&request.order_number, &request.order_date, &request.customer],
    )
    .await
    .map_err(|e| e.to_string())?;

    Ok(ShipmentOrder {
        order_number: request.order_number,
        order_date: request.order_date,
        customer: request.customer,
    })
}

#[tauri::command]
pub async fn get_shipment_orders(app: tauri::AppHandle) -> Result<Vec<ShipmentOrder>, String> {
    let db = tauri_plugin_sql::Builder::default()
        .build()
        .get_connection(&app, "sqlite:fishtouch.db")
        .await
        .map_err(|e| e.to_string())?;

    let result = db
        .select("SELECT order_number, order_date, customer FROM shipment_orders ORDER BY order_date DESC")
        .await
        .map_err(|e| e.to_string())?;

    let orders: Vec<ShipmentOrder> = result
        .into_iter()
        .map(|row| ShipmentOrder {
            order_number: row.get::<String, _>("order_number").unwrap_or_default(),
            order_date: row.get::<String, _>("order_date").unwrap_or_default(),
            customer: row.get::<String, _>("customer").unwrap_or_default(),
        })
        .collect();

    Ok(orders)
}

// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		08F16B4EDE820D5001121308 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C3418A99B295334438C69B72 /* QuartzCore.framework */; };
		43A7513FC95DF65C2492E97B /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EA5183A4521C2EB3BDAD670A /* WebKit.framework */; };
		4B206451C4F93D9BBEB06DEE /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D749A53C18AF90384FCAA7A /* UIKit.framework */; };
		5C985ECA613A821676E2EB5D /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9FA153C9615F2295ACB3D4C9 /* Metal.framework */; };
		69296760171F73C729E6A0B4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 300815452621EB93EF0ECA2F /* Assets.xcassets */; };
		6DD7B2CBAA59CBC4B7F069D4 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7930934F1D0B66AF5DA09E86 /* Security.framework */; };
		8F0CAE91554F0D07A1C3EAE1 /* libapp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EB9792889F0ED67D2A057F52 /* libapp.a */; };
		C26805C2D2C63F20EAEC44B0 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 913CEA1F78163519D0ABAC28 /* LaunchScreen.storyboard */; };
		D6A4ECB420770DE967536B32 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8949A61549761D669DBC6289 /* CoreGraphics.framework */; };
		E1E52C19DD940B7FE604E583 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 9DD68776939CDB7C40527697 /* assets */; };
		EB52A536F4CC774B3348EBD4 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 742DCC35E25735471273B621 /* MetalKit.framework */; };
		F3BD3DED615F02FC77ECF648 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = 9ACE19C2AF46413BBB003475 /* main.mm */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		300815452621EB93EF0ECA2F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		4A280D7F085264D2426C5213 /* lib.rs */ = {isa = PBXFileReference; path = lib.rs; sourceTree = "<group>"; };
		5B0B5BDF875B7390E5130FB4 /* fishtouch-store_iOS.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = "fishtouch-store_iOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		742DCC35E25735471273B621 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		7930934F1D0B66AF5DA09E86 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		7D749A53C18AF90384FCAA7A /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		7DAA4F97ED06FC4D020EED39 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		819A4223D97659229A293757 /* fishtouch-store_iOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "fishtouch-store_iOS.entitlements"; sourceTree = "<group>"; };
		8949A61549761D669DBC6289 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		8EBECE6A09943AFC815A2F0B /* bindings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bindings.h; sourceTree = "<group>"; };
		913CEA1F78163519D0ABAC28 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		9ACE19C2AF46413BBB003475 /* main.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		9DD68776939CDB7C40527697 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = SOURCE_ROOT; };
		9FA153C9615F2295ACB3D4C9 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		C3418A99B295334438C69B72 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		EA5183A4521C2EB3BDAD670A /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		EB9792889F0ED67D2A057F52 /* libapp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libapp.a; sourceTree = "<group>"; };
		FA0A776E4A6F42BAFC4886AB /* main.rs */ = {isa = PBXFileReference; path = main.rs; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2F22BD80AF885C28DEB65966 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8F0CAE91554F0D07A1C3EAE1 /* libapp.a in Frameworks */,
				D6A4ECB420770DE967536B32 /* CoreGraphics.framework in Frameworks */,
				5C985ECA613A821676E2EB5D /* Metal.framework in Frameworks */,
				EB52A536F4CC774B3348EBD4 /* MetalKit.framework in Frameworks */,
				08F16B4EDE820D5001121308 /* QuartzCore.framework in Frameworks */,
				6DD7B2CBAA59CBC4B7F069D4 /* Security.framework in Frameworks */,
				4B206451C4F93D9BBEB06DEE /* UIKit.framework in Frameworks */,
				43A7513FC95DF65C2492E97B /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		042BE83E9C0EDD1D8C0BC4A4 /* Products */ = {
			isa = PBXGroup;
			children = (
				5B0B5BDF875B7390E5130FB4 /* fishtouch-store_iOS.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		380AF23D994F36C2E1F7F7AA /* Sources */ = {
			isa = PBXGroup;
			children = (
				A5A108B59976FC97FB584088 /* fishtouch-store */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		5245D24D854BD25F47759EF4 /* fishtouch-store_iOS */ = {
			isa = PBXGroup;
			children = (
				819A4223D97659229A293757 /* fishtouch-store_iOS.entitlements */,
				7DAA4F97ED06FC4D020EED39 /* Info.plist */,
			);
			path = "fishtouch-store_iOS";
			sourceTree = "<group>";
		};
		7073F3ECE440068E13013022 /* src */ = {
			isa = PBXGroup;
			children = (
				4A280D7F085264D2426C5213 /* lib.rs */,
				FA0A776E4A6F42BAFC4886AB /* main.rs */,
			);
			name = src;
			path = ../../src;
			sourceTree = "<group>";
		};
		7EF9C1A42A533DF65B97BA81 /* Externals */ = {
			isa = PBXGroup;
			children = (
			);
			path = Externals;
			sourceTree = "<group>";
		};
		7F8BD2A551B92FE7C7189997 /* bindings */ = {
			isa = PBXGroup;
			children = (
				8EBECE6A09943AFC815A2F0B /* bindings.h */,
			);
			path = bindings;
			sourceTree = "<group>";
		};
		95430D970D2331E393DDBC6A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8949A61549761D669DBC6289 /* CoreGraphics.framework */,
				EB9792889F0ED67D2A057F52 /* libapp.a */,
				9FA153C9615F2295ACB3D4C9 /* Metal.framework */,
				742DCC35E25735471273B621 /* MetalKit.framework */,
				C3418A99B295334438C69B72 /* QuartzCore.framework */,
				7930934F1D0B66AF5DA09E86 /* Security.framework */,
				7D749A53C18AF90384FCAA7A /* UIKit.framework */,
				EA5183A4521C2EB3BDAD670A /* WebKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A5A108B59976FC97FB584088 /* fishtouch-store */ = {
			isa = PBXGroup;
			children = (
				9ACE19C2AF46413BBB003475 /* main.mm */,
				7F8BD2A551B92FE7C7189997 /* bindings */,
			);
			path = "fishtouch-store";
			sourceTree = "<group>";
		};
		A674FED685D2BE7E64100C93 = {
			isa = PBXGroup;
			children = (
				9DD68776939CDB7C40527697 /* assets */,
				300815452621EB93EF0ECA2F /* Assets.xcassets */,
				913CEA1F78163519D0ABAC28 /* LaunchScreen.storyboard */,
				7EF9C1A42A533DF65B97BA81 /* Externals */,
				5245D24D854BD25F47759EF4 /* fishtouch-store_iOS */,
				380AF23D994F36C2E1F7F7AA /* Sources */,
				7073F3ECE440068E13013022 /* src */,
				95430D970D2331E393DDBC6A /* Frameworks */,
				042BE83E9C0EDD1D8C0BC4A4 /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		D0A2EE97ECC47A1ED2C5B8B7 /* fishtouch-store_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D553B9AE5016A79F68474A35 /* Build configuration list for PBXNativeTarget "fishtouch-store_iOS" */;
			buildPhases = (
				C4B63075C68A79517C28E3CC /* Build Rust Code */,
				0AAE447FE5D790A96F4BF88B /* Sources */,
				6E1931B1BAD6CC4B53328FA1 /* Resources */,
				2F22BD80AF885C28DEB65966 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "fishtouch-store_iOS";
			packageProductDependencies = (
			);
			productName = "fishtouch-store_iOS";
			productReference = 5B0B5BDF875B7390E5130FB4 /* fishtouch-store_iOS.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9FC25A37169EFEC946B544AF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
				};
			};
			buildConfigurationList = C277A20A3B6107F963573781 /* Build configuration list for PBXProject "fishtouch-store" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = A674FED685D2BE7E64100C93;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 54;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D0A2EE97ECC47A1ED2C5B8B7 /* fishtouch-store_iOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6E1931B1BAD6CC4B53328FA1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				69296760171F73C729E6A0B4 /* Assets.xcassets in Resources */,
				C26805C2D2C63F20EAEC44B0 /* LaunchScreen.storyboard in Resources */,
				E1E52C19DD940B7FE604E583 /* assets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		C4B63075C68A79517C28E3CC /* Build Rust Code */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Build Rust Code";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(SRCROOT)/Externals/x86_64/${CONFIGURATION}/libapp.a",
				"$(SRCROOT)/Externals/arm64/${CONFIGURATION}/libapp.a",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "bun tauri ios xcode-script -v --platform ${PLATFORM_DISPLAY_NAME:?} --sdk-root ${SDKROOT:?} --framework-search-paths \"${FRAMEWORK_SEARCH_PATHS:?}\" --header-search-paths \"${HEADER_SEARCH_PATHS:?}\" --gcc-preprocessor-definitions \"${GCC_PREPROCESSOR_DEFINITIONS:-}\" --configuration ${CONFIGURATION:?} ${FORCE_COLOR} ${ARCHS:?}";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0AAE447FE5D790A96F4BF88B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3BD3DED615F02FC77ECF648 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		09DA242C72D36B089AA50B48 /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = debug;
		};
		2290F9890723907BD1EE2D99 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "fishtouch-store_iOS/fishtouch-store_iOS.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = "fishtouch-store_iOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = "com.fishtouch-store.app";
				PRODUCT_NAME = "fishtouch-store";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = release;
		};
		3DE16EF63E1AC5DC20DA053C /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = "fishtouch-store_iOS/fishtouch-store_iOS.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = "fishtouch-store_iOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = "com.fishtouch-store.app";
				PRODUCT_NAME = "fishtouch-store";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = debug;
		};
		C04484F4CB555350B9BAD40B /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C277A20A3B6107F963573781 /* Build configuration list for PBXProject "fishtouch-store" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				09DA242C72D36B089AA50B48 /* debug */,
				C04484F4CB555350B9BAD40B /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
		D553B9AE5016A79F68474A35 /* Build configuration list for PBXNativeTarget "fishtouch-store_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3DE16EF63E1AC5DC20DA053C /* debug */,
				2290F9890723907BD1EE2D99 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9FC25A37169EFEC946B544AF /* Project object */;
}

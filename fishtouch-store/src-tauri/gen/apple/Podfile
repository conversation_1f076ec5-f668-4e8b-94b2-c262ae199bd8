# Uncomment the next line to define a global platform for your project

target 'fishtouch-store_iOS' do
platform :ios, '13.0'
  # Pods for fishtouch-store_iOS
end

target 'fishtouch-store_macOS' do
platform :osx, '11.0'
  # Pods for fishtouch-store_macOS
end

# Delete the deployment target for iOS and macOS, causing it to be inherited from the Podfile
post_install do |installer|
 installer.pods_project.targets.each do |target|
  target.build_configurations.each do |config|
   config.build_settings.delete 'IPHONEOS_DEPLOYMENT_TARGET'
   config.build_settings.delete 'MACOSX_DEPLOYMENT_TARGET'
  end
 end
end
